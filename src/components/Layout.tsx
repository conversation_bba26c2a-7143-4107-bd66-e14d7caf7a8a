
import { Outlet } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { TopBar } from "@/components/TopBar";

export const Layout = () => {
  // Temporary simple layout for debugging
  const SIMPLE_LAYOUT = true; // Set to false to use full layout

  if (SIMPLE_LAYOUT) {
    return (
      <div className="min-h-screen bg-slate-50 p-8">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8 p-4 bg-blue-100 border border-blue-300 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-800">🔧 Debug Mode</h2>
            <p className="text-blue-700">Layout is in simple mode for debugging. Set SIMPLE_LAYOUT = false to restore full layout.</p>
          </div>
          <Outlet />
        </div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <div className="flex-1 flex flex-col">
          <TopBar />
          <main className="flex-1 p-4 lg:p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};
