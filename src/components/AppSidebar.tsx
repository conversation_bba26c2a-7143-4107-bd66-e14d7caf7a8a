
import { <PERSON><PERSON>, MessageSquare, BarChart3, CreditCard, Settings, Menu, TestTube } from "lucide-react";
import { NavLink } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";

const items = [
  { title: "My Bots", url: "/dashboard", icon: Bot, color: "text-blue-500", bgColor: "bg-blue-50 hover:bg-blue-100" },
  { title: "Bot Testing", url: "/testing", icon: TestTube, color: "text-purple-500", bgColor: "bg-purple-50 hover:bg-purple-100" },
  { title: "Messages", url: "/messages", icon: MessageSquare, color: "text-green-500", bgColor: "bg-green-50 hover:bg-green-100" },
  { title: "Analytics", url: "/analytics", icon: BarChart3, color: "text-orange-500", bgColor: "bg-orange-50 hover:bg-orange-100" },
  { title: "Plans", url: "/pricing", icon: CreditCard, color: "text-pink-500", bgColor: "bg-pink-50 hover:bg-pink-100" },
  { title: "Settings", url: "/settings", icon: Settings, color: "text-gray-500", bgColor: "bg-gray-50 hover:bg-gray-100" },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  return (
    <Sidebar className={`${isCollapsed ? "w-16" : "w-64"} bg-gradient-to-b from-slate-50 to-slate-100 border-r border-slate-200`} collapsible="icon">
      <div className="p-4 border-b bg-gradient-to-r from-teal-500 to-blue-600 border-slate-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center shadow-lg">
              <Bot className="w-5 h-5 text-white" />
            </div>
            {!isCollapsed && (
              <span className="font-bold text-xl text-white drop-shadow-sm">Chatlink</span>
            )}
          </div>
          {!isCollapsed && (
            <SidebarTrigger className="h-8 w-8 hover:bg-white/20 rounded-lg flex items-center justify-center transition-colors">
              <Menu className="h-4 w-4 text-white" />
            </SidebarTrigger>
          )}
        </div>
      </div>

      <SidebarContent className="bg-gradient-to-b from-slate-50 to-slate-100 p-2">
        <SidebarGroup>
          <SidebarGroupLabel className="text-slate-600 text-xs font-semibold px-4 py-3 uppercase tracking-wider">
            Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={item.url}
                      className={({ isActive }) =>
                        `flex items-center gap-3 px-4 py-3 mx-2 rounded-xl transition-all duration-300 group ${
                          isActive
                            ? "bg-gradient-to-r from-teal-500 to-blue-600 text-white shadow-lg shadow-teal-500/25 border-l-4 border-white/30"
                            : "text-slate-700 hover:bg-white hover:shadow-md hover:shadow-slate-200/50 hover:scale-[1.02]"
                        }`
                      }
                    >
                      <div className={`p-2 rounded-lg transition-all duration-300 ${
                        isActive
                          ? "bg-white/20"
                          : `${item.bgColor} group-hover:scale-110`
                      }`}>
                        <item.icon className={`w-4 h-4 flex-shrink-0 transition-colors duration-300 ${
                          isActive ? "text-white" : item.color
                        }`} />
                      </div>
                      {!isCollapsed && (
                        <span className={`font-medium text-sm transition-colors duration-300 ${
                          isActive ? "text-white" : "text-slate-700 group-hover:text-slate-900"
                        }`}>
                          {item.title}
                        </span>
                      )}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
