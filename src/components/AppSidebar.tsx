
import { <PERSON><PERSON>, MessageSquare, BarChart3, CreditCard, Settings, Menu, TestTube } from "lucide-react";
import { NavLink } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";

const items = [
  { title: "My Bots", url: "/dashboard", icon: Bot, color: "text-blue-500", bgColor: "bg-blue-50 hover:bg-blue-100" },
  { title: "Bot Testing", url: "/testing", icon: TestTube, color: "text-purple-500", bgColor: "bg-purple-50 hover:bg-purple-100" },
  { title: "Messages", url: "/messages", icon: MessageSquare, color: "text-green-500", bgColor: "bg-green-50 hover:bg-green-100" },
  { title: "Analytics", url: "/analytics", icon: BarChart3, color: "text-orange-500", bgColor: "bg-orange-50 hover:bg-orange-100" },
  { title: "Plans", url: "/pricing", icon: CreditCard, color: "text-pink-500", bgColor: "bg-pink-50 hover:bg-pink-100" },
  { title: "Settings", url: "/settings", icon: Settings, color: "text-gray-500", bgColor: "bg-gray-50 hover:bg-gray-100" },
];

export function AppSidebar() {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  return (
    <Sidebar className={`${isCollapsed ? "w-16" : "w-64"} bg-white border-r border-gray-200`} collapsible="icon">
      <div className="p-4 border-b bg-gradient-to-r from-teal-500 to-blue-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center">
              <Bot className="w-5 h-5 text-white" />
            </div>
            {!isCollapsed && (
              <span className="font-bold text-xl text-white">Chatlink</span>
            )}
          </div>
          {!isCollapsed && (
            <SidebarTrigger className="h-8 w-8 hover:bg-white/20 rounded-lg flex items-center justify-center">
              <Menu className="h-4 w-4 text-white" />
            </SidebarTrigger>
          )}
        </div>
      </div>

      <SidebarContent className="bg-gray-50 p-2">
        <SidebarGroup>
          <SidebarGroupLabel className="text-gray-600 text-xs font-semibold px-4 py-3 uppercase tracking-wider">
            Navigation
          </SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={item.url}
                    >
                      {({ isActive }) => (
                        <div className={`flex items-center gap-3 px-4 py-3 mx-2 rounded-lg transition-all duration-200 ${
                          isActive
                            ? "bg-gradient-to-r from-teal-500 to-blue-600 text-white shadow-lg"
                            : "text-gray-700 hover:bg-white hover:shadow-md"
                        }`}>
                          <div className={`p-2 rounded-lg ${
                            isActive
                              ? "bg-white/20"
                              : item.bgColor
                          }`}>
                            <item.icon className={`w-4 h-4 flex-shrink-0 ${
                              isActive ? "text-white" : item.color
                            }`} />
                          </div>
                          {!isCollapsed && (
                            <span className={`font-medium text-sm ${
                              isActive ? "text-white" : "text-gray-700"
                            }`}>
                              {item.title}
                            </span>
                          )}
                        </div>
                      )}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
