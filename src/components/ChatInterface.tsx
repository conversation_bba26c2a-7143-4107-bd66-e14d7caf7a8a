import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Send, FileText } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface UploadedFile {
  id: string;
  file: File;
  name: string;
  size: number;
}

export const ChatInterface = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: "Hi there! 👋 Tell me about your business — what do you do, what services do you offer, and what should your chatbot know?",
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState("");
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [hasUserInput, setHasUserInput] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue("");
    setHasUserInput(true);

    // Simulate assistant response after a brief delay
    setTimeout(() => {
      const responses = [
        "That sounds interesting! Tell me more about your services.",
        "Great! What specific questions do your customers usually ask?",
        "Perfect! Any particular tone or style you'd like your chatbot to have?",
        "Excellent! What else should I know about your business?"
      ];
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, assistantMessage]);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const validFiles = files.filter(file => 
        ['application/pdf', 'text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type)
      );

      validFiles.forEach(file => {
        const uploadedFile: UploadedFile = {
          id: Date.now().toString() + Math.random(),
          file,
          name: file.name,
          size: file.size
        };

        setUploadedFiles(prev => [...prev, uploadedFile]);
        setHasUserInput(true);

        // Add file upload message
        const fileMessage: Message = {
          id: Date.now().toString() + Math.random(),
          type: 'user',
          content: `📄 \`${file.name}\` uploaded`,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, fileMessage]);

        // Assistant acknowledgment
        setTimeout(() => {
          const ackMessage: Message = {
            id: Date.now().toString() + Math.random(),
            type: 'assistant',
            content: "Great! I've received your file. Feel free to tell me more about your business or upload additional files.",
            timestamp: new Date()
          };
          setMessages(prev => [...prev, ackMessage]);
        }, 500);
      });
    }
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleGenerateBot = async () => {
    if (!hasUserInput) return;
    
    try {
      // Compile chat context for training
      const chatContext = messages
        .filter(msg => msg.type === 'user')
        .map(msg => msg.content)
        .join('\n\n');
      
      toast({
        title: "Generating Your Chatbot",
        description: "Creating your personalized chatbot...",
      });
      
      // For now, simulate the bot creation process
      // In a real implementation, you would call the actual hooks here
      setTimeout(() => {
        toast({
          title: "Chatbot Created!",
          description: "Your chatbot has been successfully created.",
        });
        
        navigate('/dashboard');
      }, 2000);
      
    } catch (error) {
      console.error('Error generating bot:', error);
      toast({
        title: "Error",
        description: "Failed to create chatbot. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="bg-white rounded-2xl shadow-2xl overflow-hidden ring-1 ring-slate-200">
      {/* Header */}
      <div className="bg-gradient-to-r from-teal-500 via-blue-500 to-purple-600 p-8 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-3 drop-shadow-sm">
            Let's Build Your Chatbot
          </h2>
          <p className="text-white/90 text-lg">
            Chat with me about your business and upload relevant files
          </p>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="h-96 overflow-y-auto p-6 space-y-4 bg-gradient-to-b from-slate-50 to-white">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} animate-fade-in`}
          >
            <div
              className={`max-w-[80%] p-4 rounded-2xl shadow-lg ${
                message.type === 'user'
                  ? 'bg-gradient-to-r from-teal-500 to-blue-600 text-white ml-4'
                  : 'bg-white border border-slate-200 mr-4 shadow-md'
              }`}
            >
              <p className="text-sm leading-relaxed">{message.content}</p>
              <span className={`text-xs opacity-70 mt-2 block ${
                message.type === 'user' ? 'text-white/70' : 'text-slate-500'
              }`}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Uploaded Files Display */}
      {uploadedFiles.length > 0 && (
        <div className="px-6 py-3 border-t border-border bg-muted/10">
          <div className="flex flex-wrap gap-2">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center gap-2 bg-success/10 text-success-foreground px-3 py-1 rounded-full text-sm"
              >
                <FileText className="w-4 h-4" />
                <span>{file.name}</span>
                <span className="text-xs opacity-70">
                  ({(file.size / 1024 / 1024).toFixed(1)}MB)
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="p-6 border-t border-slate-200 bg-slate-50">
        <div className="flex items-center gap-4">
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.txt,.docx"
            onChange={handleFileSelect}
            className="hidden"
          />

          <Button
            variant="outline"
            size="icon"
            onClick={handleFileUpload}
            className="shrink-0 hover:bg-slate-100 border-slate-300 rounded-xl transition-all duration-200 hover:scale-105 hover:shadow-md"
          >
            <Plus className="w-4 h-4 text-slate-600" />
          </Button>

          <div className="flex-1 relative">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Type a message…"
              className="pr-14 rounded-2xl border-2 border-slate-200 focus:border-teal-500 focus:ring-2 focus:ring-teal-500/20 bg-white shadow-sm text-slate-700 placeholder:text-slate-400"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim()}
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 h-9 w-9 rounded-xl bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 disabled:from-slate-300 disabled:to-slate-400 transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Generate Button */}
        {hasUserInput && (
          <div className="mt-8 animate-fade-in">
            <Button
              onClick={handleGenerateBot}
              className="w-full bg-gradient-to-r from-teal-500 via-blue-500 to-purple-600 hover:from-teal-600 hover:via-blue-600 hover:to-purple-700 text-white font-bold py-4 rounded-2xl shadow-2xl hover:shadow-2xl transition-all duration-300 hover:scale-[1.02] text-lg relative overflow-hidden group"
            >
              <span className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              <span className="relative z-10 flex items-center justify-center gap-2">
                ✨ Generate My Chatbot
              </span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};