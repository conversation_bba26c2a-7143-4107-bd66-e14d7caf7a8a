import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Send, FileText } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface UploadedFile {
  id: string;
  file: File;
  name: string;
  size: number;
}

export const ChatInterface = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: "Hi there! 👋 Tell me about your business — what do you do, what services do you offer, and what should your chatbot know?",
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState("");
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [hasUserInput, setHasUserInput] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setInputValue("");
    setHasUserInput(true);

    // Simulate assistant response after a brief delay
    setTimeout(() => {
      const responses = [
        "That sounds interesting! Tell me more about your services.",
        "Great! What specific questions do your customers usually ask?",
        "Perfect! Any particular tone or style you'd like your chatbot to have?",
        "Excellent! What else should I know about your business?"
      ];
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, assistantMessage]);
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      const validFiles = files.filter(file => 
        ['application/pdf', 'text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type)
      );

      validFiles.forEach(file => {
        const uploadedFile: UploadedFile = {
          id: Date.now().toString() + Math.random(),
          file,
          name: file.name,
          size: file.size
        };

        setUploadedFiles(prev => [...prev, uploadedFile]);
        setHasUserInput(true);

        // Add file upload message
        const fileMessage: Message = {
          id: Date.now().toString() + Math.random(),
          type: 'user',
          content: `📄 \`${file.name}\` uploaded`,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, fileMessage]);

        // Assistant acknowledgment
        setTimeout(() => {
          const ackMessage: Message = {
            id: Date.now().toString() + Math.random(),
            type: 'assistant',
            content: "Great! I've received your file. Feel free to tell me more about your business or upload additional files.",
            timestamp: new Date()
          };
          setMessages(prev => [...prev, ackMessage]);
        }, 500);
      });
    }
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleGenerateBot = () => {
    toast({
      title: "Generating Your Chatbot",
      description: "Your chatbot is being created! Redirecting to dashboard...",
    });

    // Simulate bot generation and redirect to dashboard
    setTimeout(() => {
      navigate('/dashboard');
    }, 2000);
  };

  return (
    <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary to-primary-hover p-6 text-white">
        <h2 className="text-2xl font-bold mb-2">
          Let's Build Your Chatbot
        </h2>
        <p className="text-primary-foreground/90">
          Chat with me about your business and upload relevant files
        </p>
      </div>

      {/* Chat Messages */}
      <div className="h-96 overflow-y-auto p-6 space-y-4 bg-muted/20">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} animate-fade-in`}
          >
            <div
              className={`max-w-[80%] p-4 rounded-2xl ${
                message.type === 'user'
                  ? 'bg-primary text-primary-foreground ml-4'
                  : 'bg-white border border-border mr-4 shadow-sm'
              }`}
            >
              <p className="text-sm leading-relaxed">{message.content}</p>
              <span className={`text-xs opacity-70 mt-2 block ${
                message.type === 'user' ? 'text-primary-foreground/70' : 'text-muted-foreground'
              }`}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Uploaded Files Display */}
      {uploadedFiles.length > 0 && (
        <div className="px-6 py-3 border-t border-border bg-muted/10">
          <div className="flex flex-wrap gap-2">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center gap-2 bg-success/10 text-success-foreground px-3 py-1 rounded-full text-sm"
              >
                <FileText className="w-4 h-4" />
                <span>{file.name}</span>
                <span className="text-xs opacity-70">
                  ({(file.size / 1024 / 1024).toFixed(1)}MB)
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="p-6 border-t border-border bg-white">
        <div className="flex items-center gap-3">
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.txt,.docx"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          <Button
            variant="outline"
            size="icon"
            onClick={handleFileUpload}
            className="shrink-0 hover:bg-accent"
          >
            <Plus className="w-4 h-4" />
          </Button>

          <div className="flex-1 relative">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Type a message…"
              className="pr-12 rounded-full border-2 focus:border-primary"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim()}
              size="icon"
              className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 rounded-full"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Generate Button */}
        {hasUserInput && (
          <div className="mt-6 animate-fade-in">
            <Button
              onClick={handleGenerateBot}
              className="w-full bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white font-semibold py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Generate My Chatbot
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};