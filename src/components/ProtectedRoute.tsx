
import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePhoneVerification } from '@/hooks/usePhoneVerification';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading: authLoading } = useAuth();
  const { needsPhoneVerification, loading: phoneLoading } = usePhoneVerification();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!authLoading && !user) {
      navigate('/signin');
      return;
    }

    // If user is authenticated but needs phone verification
    // and they're not already on the phone verification page
    if (
      user && 
      !phoneLoading && 
      needsPhoneVerification && 
      location.pathname !== '/phone-verification'
    ) {
      navigate('/phone-verification');
    }
  }, [user, authLoading, needsPhoneVerification, phoneLoading, navigate, location.pathname]);

  if (authLoading || phoneLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-teal-500"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return <>{children}</>;
};
