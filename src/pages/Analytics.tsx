
import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, MessageSquare, Clock, Target } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  <PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';

const Analytics = () => {
  const monthlyData = [
    { month: 'Jan', messages: 1200, users: 89 },
    { month: 'Feb', messages: 1800, users: 124 },
    { month: 'Mar', messages: 2400, users: 157 },
    { month: 'Apr', messages: 2100, users: 143 },
    { month: 'May', messages: 2800, users: 189 },
    { month: 'Jun', messages: 3200, users: 221 }
  ];

  const channelData = [
    { name: 'WhatsApp', value: 45, color: '#25D366' },
    { name: 'Facebook', value: 35, color: '#1877F2' },
    { name: 'SMS', value: 20, color: '#FF6B6B' }
  ];

  const botPerformance = [
    { name: 'Restaurant Assistant', messages: 1850, satisfaction: 4.8, responseTime: '2.3s' },
    { name: 'Plumbing Services', messages: 1200, satisfaction: 4.6, responseTime: '1.8s' },
    { name: 'Hair Salon Bot', messages: 650, satisfaction: 4.4, responseTime: '3.1s' }
  ];

  const metrics = [
    {
      title: "Total Conversations",
      value: "3,847",
      change: "+23%",
      icon: MessageSquare,
      color: "text-blue-600"
    },
    {
      title: "Active Users",
      value: "1,284",
      change: "+18%",
      icon: Users,
      color: "text-green-600"
    },
    {
      title: "Avg Response Time",
      value: "2.1s",
      change: "-12%",
      icon: Clock,
      color: "text-purple-600"
    },
    {
      title: "Resolution Rate",
      value: "89%",
      change: "+5%",
      icon: Target,
      color: "text-orange-600"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600 mt-1">Track your chatbot performance and insights</p>
        </div>
        <div className="flex gap-2">
          <Select>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Last 30 days" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">Export Report</Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                  <p className="text-3xl font-bold text-gray-900 mt-1">{metric.value}</p>
                  <p className={`text-sm mt-1 ${metric.change.startsWith('+') ? 'text-green-600' : metric.change.startsWith('-') ? 'text-red-600' : 'text-gray-600'}`}>
                    {metric.change} vs last period
                  </p>
                </div>
                <div className="p-3 rounded-lg bg-gray-50">
                  <metric.icon className={`w-6 h-6 ${metric.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Messages Over Time */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Messages & Users Over Time
            </CardTitle>
            <CardDescription>Monthly conversation and user trends</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="messages" fill="#14B8A6" name="Messages" />
                <Bar dataKey="users" fill="#3B82F6" name="Users" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Channel Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Channel Distribution</CardTitle>
            <CardDescription>Messages by communication channel</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={channelData}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {channelData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Bot Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Bot Performance
          </CardTitle>
          <CardDescription>Individual chatbot statistics and metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {botPerformance.map((bot, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-teal-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <MessageSquare className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{bot.name}</h3>
                    <p className="text-sm text-gray-600">{bot.messages} total messages</p>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{bot.satisfaction}/5.0</p>
                    <p className="text-xs text-gray-500">Satisfaction</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-900">{bot.responseTime}</p>
                    <p className="text-xs text-gray-500">Avg Response</p>
                  </div>
                  <Badge 
                    variant="secondary"
                    className="bg-green-100 text-green-800"
                  >
                    Active
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
