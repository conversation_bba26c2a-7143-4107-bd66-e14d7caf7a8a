
import { <PERSON><PERSON>, MessageS<PERSON><PERSON>, Users, FileText, MoreHorizontal, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Dashboard = () => {
  const bots = [
    {
      id: 1,
      name: "Restaurant Assistant",
      channels: ["WhatsApp", "Facebook"],
      status: "active",
      messages: 1250,
      users: 89,
      created: "2 days ago"
    },
    {
      id: 2,
      name: "Plumbing Services Bot",
      channels: ["SMS", "WhatsApp"],
      status: "active",
      messages: 890,
      users: 45,
      created: "1 week ago"
    },
    {
      id: 3,
      name: "Hair Salon Bot",
      channels: ["Facebook"],
      status: "inactive",
      messages: 340,
      users: 23,
      created: "2 weeks ago"
    }
  ];

  const stats = [
    {
      title: "Total Messages",
      value: "2,480",
      change: "+12%",
      icon: MessageSquare,
      color: "text-blue-600"
    },
    {
      title: "Active Users",
      value: "157",
      change: "+8%",
      icon: Users,
      color: "text-green-600"
    },
    {
      title: "Active Bots",
      value: "2",
      change: "0%",
      icon: Bot,
      color: "text-purple-600"
    },
    {
      title: "Files Processed",
      value: "8",
      change: "+2",
      icon: FileText,
      color: "text-orange-600"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 animate-fade-in">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground mt-2">Manage your chatbots and view performance</p>
        </div>
        <Button className="bg-primary hover:bg-primary-hover text-primary-foreground shadow-md hover:shadow-lg transition-all duration-300">
          <Plus className="w-4 h-4 mr-2" />
          Create New Bot
        </Button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="card-elevated border-0 bg-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-3xl font-bold text-foreground mt-2">{stat.value}</p>
                  <p className="text-sm text-success mt-2 font-medium">{stat.change} from last month</p>
                </div>
                <div className="p-3 rounded-xl bg-primary/10">
                  <stat.icon className="w-6 h-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Bots Overview */}
      <Card className="card-elevated border-0">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-foreground">
            <Bot className="w-5 h-5 text-primary" />
            Your Chatbots
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Manage and monitor your active chatbots
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {bots.map((bot) => (
              <div key={bot.id} className="flex items-center justify-between p-4 border border-border rounded-xl hover:bg-accent/50 transition-all duration-300 card-interactive">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary-hover rounded-xl flex items-center justify-center shadow-md">
                    <Bot className="w-6 h-6 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">{bot.name}</h3>
                    <div className="flex items-center gap-2 mt-2">
                      {bot.channels.map((channel) => (
                        <Badge key={channel} variant="secondary" className="text-xs font-medium">
                          {channel}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  <div className="text-center hidden sm:block">
                    <p className="text-sm font-semibold text-foreground">{bot.messages}</p>
                    <p className="text-xs text-muted-foreground">Messages</p>
                  </div>
                  <div className="text-center hidden sm:block">
                    <p className="text-sm font-semibold text-foreground">{bot.users}</p>
                    <p className="text-xs text-muted-foreground">Users</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={bot.status === 'active' ? 'default' : 'secondary'}
                      className={bot.status === 'active' ? 'bg-success/10 text-success border-success/20' : ''}
                    >
                      {bot.status}
                    </Badge>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="hover:bg-accent">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-popover border-border shadow-lg">
                        <DropdownMenuItem className="hover:bg-accent">Edit Bot</DropdownMenuItem>
                        <DropdownMenuItem className="hover:bg-accent">View Analytics</DropdownMenuItem>
                        <DropdownMenuItem className="hover:bg-accent">Test Bot</DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive hover:bg-destructive/10">Delete Bot</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;
