
import { ChatInterface } from "@/components/ChatInterface";
import { But<PERSON> } from "@/components/ui/button";
import { MessageSquare, Zap, Shield } from "lucide-react";
import { Link } from "react-router-dom";

const Landing = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-teal-50">
      {/* Header */}
      <header className="px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-teal-500 to-blue-600 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-white" />
            </div>
            <span className="font-bold text-2xl text-gray-800">Chatlink</span>
          </div>
          <Button variant="outline" className="border-teal-500 text-teal-600 hover:bg-teal-50" asChild>
            <Link to="/signin">Sign In</Link>
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-16">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Content */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Chat,
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-blue-600">
                  Create,
                </span>
                <br />
                Deploy
              </h1>
              
              <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                Describe your business in conversation and upload documents to create intelligent chatbots for WhatsApp, Facebook Messenger, and SMS.
              </p>
            </div>

            {/* Features */}
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                  <Zap className="w-6 h-6 text-teal-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Instant Setup</h3>
                  <p className="text-gray-600">Get your chatbot ready in minutes, not hours</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Multi-Platform</h3>
                  <p className="text-gray-600">Works on WhatsApp, Facebook, and SMS</p>
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Shield className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Secure & Private</h3>
                  <p className="text-gray-600">Your data stays protected and confidential</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Chat Interface */}
          <ChatInterface />
        </div>
      </div>

      {/* Trusted By Section */}
      <div className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-6 text-center">
          <p className="text-gray-500 mb-8">Trusted by 500+ small businesses worldwide</p>
          <div className="flex justify-center items-center gap-8 opacity-60">
            <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">Logo</div>
            <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">Logo</div>
            <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">Logo</div>
            <div className="w-24 h-12 bg-gray-200 rounded flex items-center justify-center">Logo</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Landing;
