
import { useState, useEffect } from "react";
import { Settings as Setting<PERSON><PERSON><PERSON>, <PERSON>r, Bell, CreditCard, Shield, Key } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

const Settings = () => {
  const { user } = useAuth();
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    businessName: "",
    timezone: "est"
  });

  // Notification preferences state
  const [notifications, setNotifications] = useState({
    newMessages: true,
    botPerformance: true,
    usageWarnings: true,
    weeklyReports: false
  });

  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("profile");

  // Load user data when component mounts or user changes
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.user_metadata?.first_name || "",
        lastName: user.user_metadata?.last_name || "",
        email: user.email || "",
        businessName: user.user_metadata?.business_name || "",
        timezone: user.user_metadata?.timezone || "est"
      });
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNotificationChange = (field: string, value: boolean) => {
    setNotifications(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveProfile = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        data: {
          first_name: formData.firstName,
          last_name: formData.lastName,
          business_name: formData.businessName,
          timezone: formData.timezone
        }
      });

      if (error) throw error;

      toast({
        title: "Profile Updated",
        description: "Your profile information has been successfully updated.",
      });
    } catch (error: any) {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const settingsMenuItems = [
    { icon: User, label: "Profile", id: "profile" },
    { icon: Bell, label: "Notifications", id: "notifications" },
    { icon: Key, label: "API Keys", id: "api" },
    { icon: CreditCard, label: "Billing", id: "billing" },
    { icon: Shield, label: "Security", id: "security" }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-foreground">Settings</h1>
        <p className="text-muted-foreground mt-1">Manage your account and chatbot preferences</p>
      </div>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Settings Navigation */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <SettingsIcon className="w-5 h-5" />
              Settings Menu
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="space-y-1">
              {settingsMenuItems.map((item) => (
                <div
                  key={item.id}
                  onClick={() => setActiveTab(item.id)}
                  className={`flex items-center gap-3 p-2 mx-2 rounded-md cursor-pointer transition-colors ${
                    activeTab === item.id ? 'bg-accent text-accent-foreground border-r-2 border-primary' : 'text-muted-foreground hover:bg-accent/50'
                  }`}
                >
                  <item.icon className="w-4 h-4" />
                  <span className="font-medium">{item.label}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Settings Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Profile Settings */}
          {activeTab === "profile" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Profile Information
                </CardTitle>
                <CardDescription>Update your personal information and business details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange("firstName", e.target.value)}
                      placeholder="Enter your first name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange("lastName", e.target.value)}
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    disabled
                    className="bg-gray-50"
                  />
                  <p className="text-xs text-gray-500 mt-1">Email cannot be changed here. Contact support if needed.</p>
                </div>
                <div>
                  <Label htmlFor="business">Business Name</Label>
                  <Input
                    id="business"
                    value={formData.businessName}
                    onChange={(e) => handleInputChange("businessName", e.target.value)}
                    placeholder="Enter your business name"
                  />
                </div>
                <div>
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select value={formData.timezone} onValueChange={(value) => handleInputChange("timezone", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select timezone" />
                    </SelectTrigger>
                    <SelectContent className="bg-white">
                      <SelectItem value="est">Eastern Time (ET)</SelectItem>
                      <SelectItem value="cst">Central Time (CT)</SelectItem>
                      <SelectItem value="mst">Mountain Time (MT)</SelectItem>
                      <SelectItem value="pst">Pacific Time (PT)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button onClick={handleSaveProfile} disabled={loading}>
                  {loading ? "Saving..." : "Save Changes"}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Notification Settings */}
          {activeTab === "notifications" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="w-5 h-5" />
                  Notification Preferences
                </CardTitle>
                <CardDescription>Choose what notifications you want to receive</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">New Messages</p>
                    <p className="text-sm text-gray-600">Get notified when users send messages to your bots</p>
                  </div>
                  <Switch
                    checked={notifications.newMessages}
                    onCheckedChange={(checked) => handleNotificationChange("newMessages", checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Bot Performance Alerts</p>
                    <p className="text-sm text-gray-600">Receive alerts about bot performance issues</p>
                  </div>
                  <Switch
                    checked={notifications.botPerformance}
                    onCheckedChange={(checked) => handleNotificationChange("botPerformance", checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Usage Warnings</p>
                    <p className="text-sm text-gray-600">Get notified when approaching token limits</p>
                  </div>
                  <Switch
                    checked={notifications.usageWarnings}
                    onCheckedChange={(checked) => handleNotificationChange("usageWarnings", checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Weekly Reports</p>
                    <p className="text-sm text-gray-600">Receive weekly performance summaries</p>
                  </div>
                  <Switch
                    checked={notifications.weeklyReports}
                    onCheckedChange={(checked) => handleNotificationChange("weeklyReports", checked)}
                  />
                </div>
                <Button
                  onClick={() => {
                    toast({
                      title: "Notification Preferences Updated",
                      description: "Your notification settings have been saved.",
                    });
                  }}
                  className="mt-4"
                >
                  Save Notification Settings
                </Button>
              </CardContent>
            </Card>
          )}

          {/* API Keys */}
          {activeTab === "api" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="w-5 h-5" />
                  API Keys
                </CardTitle>
                <CardDescription>Manage your API keys for integrations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">WhatsApp Business API</p>
                    <p className="text-sm text-gray-600">sk-...d4f2</p>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">Active</Badge>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">Facebook Messenger</p>
                    <p className="text-sm text-gray-600">Not connected</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      toast({
                        title: "Coming Soon",
                        description: "Facebook Messenger integration will be available soon.",
                      });
                    }}
                  >
                    Connect
                  </Button>
                </div>
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">SMS Gateway</p>
                    <p className="text-sm text-gray-600">Not connected</p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      toast({
                        title: "Coming Soon",
                        description: "SMS Gateway integration will be available soon.",
                      });
                    }}
                  >
                    Connect
                  </Button>
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    toast({
                      title: "API Key Generated",
                      description: "A new API key has been generated and sent to your email.",
                    });
                  }}
                >
                  Generate New API Key
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Billing */}
          {activeTab === "billing" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="w-5 h-5" />
                  Billing & Subscription
                </CardTitle>
                <CardDescription>Manage your subscription and billing information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 border rounded-lg bg-blue-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-blue-800">Current Plan: Free</h4>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">Active</Badge>
                  </div>
                  <p className="text-sm text-blue-600 mb-3">
                    You're currently on the free plan with 500 messages per month.
                  </p>
                  <Button
                    onClick={() => {
                      toast({
                        title: "Redirecting to Pricing",
                        description: "Taking you to our pricing page to upgrade your plan.",
                      });
                    }}
                  >
                    Upgrade Plan
                  </Button>
                </div>
                <div className="space-y-3">
                  <h4 className="font-medium">Usage This Month</h4>
                  <div className="flex justify-between text-sm">
                    <span>Messages Used</span>
                    <span>245 / 500</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '49%' }}></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Security */}
          {activeTab === "security" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Security Settings
                </CardTitle>
                <CardDescription>Manage your account security and privacy</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Change Password</h4>
                    <p className="text-sm text-gray-600 mb-3">Update your password to keep your account secure</p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        toast({
                          title: "Password Reset",
                          description: "A password reset link has been sent to your email.",
                        });
                      }}
                    >
                      Send Password Reset Email
                    </Button>
                  </div>
                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-2 text-red-600">Danger Zone</h4>
                    <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                      <h5 className="font-medium text-red-800 mb-2">Delete Account</h5>
                      <p className="text-sm text-red-600 mb-3">
                        Permanently delete your account and all associated data. This action cannot be undone.
                      </p>
                      <Button
                        variant="destructive"
                        onClick={() => {
                          toast({
                            title: "Account Deletion",
                            description: "Please contact support to delete your account.",
                            variant: "destructive",
                          });
                        }}
                      >
                        Delete Account
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
