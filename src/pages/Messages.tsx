
import { Message<PERSON><PERSON>re, Filter, <PERSON>, User, <PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const Messages = () => {
  const conversations = [
    {
      id: 1,
      user: "<PERSON>",
      bot: "Restaurant Assistant",
      channel: "WhatsApp",
      lastMessage: "What are your opening hours?",
      timestamp: "2 hours ago",
      status: "resolved"
    },
    {
      id: 2,
      user: "<PERSON>",
      bot: "Plumbing Services Bot",
      channel: "SMS",
      lastMessage: "I need emergency plumbing service",
      timestamp: "4 hours ago",
      status: "active"
    },
    {
      id: 3,
      user: "<PERSON>",
      bot: "Hair Salon Bot",
      channel: "Facebook",
      lastMessage: "Can I book an appointment for tomorrow?",
      timestamp: "1 day ago",
      status: "resolved"
    }
  ];

  const messages = [
    { sender: "user", text: "Hi, what are your opening hours?", time: "10:30 AM" },
    { sender: "bot", text: "Hello! We're open Monday-Friday 9AM-6PM, and weekends 10AM-4PM. How can I help you today?", time: "10:31 AM" },
    { sender: "user", text: "Do you take walk-ins?", time: "10:32 AM" },
    { sender: "bot", text: "Yes, we accept walk-ins! However, I'd recommend booking an appointment to guarantee your preferred time slot.", time: "10:32 AM" }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Messages</h1>
          <p className="text-gray-600 mt-1">View and manage bot conversations</p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder="Search conversations..."
                className="w-full pl-10"
              />
            </div>
            <Select>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Bots" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value="all">All Bots</SelectItem>
                <SelectItem value="restaurant">Restaurant Assistant</SelectItem>
                <SelectItem value="plumbing">Plumbing Services</SelectItem>
                <SelectItem value="salon">Hair Salon Bot</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Channels" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem value="all">All Channels</SelectItem>
                <SelectItem value="whatsapp">WhatsApp</SelectItem>
                <SelectItem value="facebook">Facebook</SelectItem>
                <SelectItem value="sms">SMS</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Conversations List */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Recent Conversations
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="space-y-1">
              {conversations.map((conv) => (
                <div
                  key={conv.id}
                  className="p-4 hover:bg-gray-50 cursor-pointer border-l-4 border-l-teal-500"
                >
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-900">{conv.user}</h4>
                    <Badge
                      variant={conv.status === 'active' ? 'default' : 'secondary'}
                      className={conv.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                    >
                      {conv.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{conv.bot}</p>
                  <p className="text-sm text-gray-500 truncate">{conv.lastMessage}</p>
                  <div className="flex justify-between items-center mt-2">
                    <Badge variant="outline" className="text-xs">
                      {conv.channel}
                    </Badge>
                    <span className="text-xs text-gray-400">{conv.timestamp}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Message Thread */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              John Smith - Restaurant Assistant
            </CardTitle>
            <div className="flex gap-2">
              <Badge variant="outline">WhatsApp</Badge>
              <Badge variant="secondary">Resolved</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 h-96 overflow-y-auto mb-4">
              {messages.map((message, index) => (
                <div
                  key={index}
                  className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.sender === 'user'
                        ? 'bg-teal-500 text-white'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {message.sender === 'user' ? (
                        <User className="w-3 h-3" />
                      ) : (
                        <Bot className="w-3 h-3" />
                      )}
                      <span className="text-xs opacity-75">{message.time}</span>
                    </div>
                    <p className="text-sm">{message.text}</p>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Quick Actions */}
            <div className="flex gap-2 pt-4 border-t">
              <Button variant="outline" size="sm">Mark as Resolved</Button>
              <Button variant="outline" size="sm">Transfer to Human</Button>
              <Button variant="outline" size="sm">Export Chat</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Messages;
